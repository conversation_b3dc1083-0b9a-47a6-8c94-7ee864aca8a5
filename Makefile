CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2 -g
SRCDIR = src
SOURCES = $(SRCDIR)/main.cpp
TARGET = netvar_scanner

# Include directories
INCLUDES = -I$(SRCDIR)

# Build target
$(TARGET): $(SOURCES)
	$(CXX) $(CXXFLAGS) $(INCLUDES) $(SOURCES) -o $(TARGET)

# Clean build files
clean:
	rm -f $(TARGET)

# Install dependencies (for Arch Linux)
deps:
	@echo "Installing dependencies for Arch Linux..."
	sudo pacman -S --needed base-devel gdb

# Run the scanner
run: $(TARGET)
	@echo "Running netvar scanner..."
	@echo "Make sure CS2 is running with -insecure and -allow_third_party_software flags!"
	sudo ./$(TARGET)

# Debug build
debug: CXXFLAGS += -DDEBUG -g3
debug: $(TARGET)

.PHONY: clean deps run debug
