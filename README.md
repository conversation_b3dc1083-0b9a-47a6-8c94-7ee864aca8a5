# CS2 Netvar Manager

A real-time netvar scanner for Counter-Strike 2 on Linux. This tool dynamically finds network variable offsets by parsing CS2's ClientClasses and RecvTables in real-time.

## Features

- **Real-time netvar scanning**: Dynamically finds offsets without static offset files
- **ClientClass parsing**: Loops through all ClientClasses in CS2's memory
- **RecvTable/RecvProp parsing**: Recursively parses network variable tables
- **Pattern scanning**: Uses byte pattern matching to find critical game structures
- **Linux support**: Designed specifically for Linux (Arch) systems

## Requirements

- Linux (tested on Arch)
- CS2 running with `-insecure` and `-allow_third_party_software` flags
- Root privileges or `CAP_SYS_PTRACE` capability
- GCC with C++17 support

## Installation

1. Install dependencies:
```bash
make deps
```

2. Build the scanner:
```bash
make
```

## Usage

1. **Start CS2 with required flags**:
```bash
steam://rungame/730/76561202255233023//-insecure%20-allow_third_party_software
```
Or add these launch options in Steam: `-insecure -allow_third_party_software`

2. **Run the netvar scanner**:
```bash
make run
```
Or manually:
```bash
sudo ./netvar_scanner
```

## How It Works

1. **Process Attachment**: Finds and attaches to the CS2 process using ptrace
2. **Module Discovery**: Locates the client.so module in memory
3. **Pattern Scanning**: Searches for ClientClassHead pointer using byte patterns
4. **ClientClass Parsing**: Iterates through the linked list of ClientClasses
5. **RecvTable Parsing**: Recursively parses RecvTables and RecvProps to find offsets
6. **Real-time Updates**: Continuously scans to detect offset changes

## Important Notes

- **Educational Purpose Only**: This is for learning game internals and reverse engineering
- **Private Games Only**: Use only in private lobbies with bots, never in public matches
- **VAC Disabled**: The `-insecure` flag disables VAC and prevents matchmaking
- **Root Required**: Memory access requires elevated privileges

## Troubleshooting

### "CS2 process not found"
- Make sure CS2 is running
- Check the process name with: `ps aux | grep cs2`
- The process might be named differently on your system

### "Failed to attach to CS2 process"
- Run with sudo: `sudo ./netvar_scanner`
- Or set capabilities: `sudo setcap cap_sys_ptrace+ep ./netvar_scanner`

### "Could not find client.so module"
- Make sure CS2 is fully loaded (not just in menu)
- Try joining a bot match first

### "Could not find ClientClassHead"
- The byte patterns may need updating for your CS2 version
- Check the pattern_scanner.h file and update patterns if needed
- CS2 updates frequently change these patterns

## Output Example

```
=== CS2 Netvar Manager ===
[INFO] Found CS2 process (PID: 12345)
[INFO] Successfully attached to CS2!
[INFO] client.so base: 0x7f1234567000
[INFO] Found ClientClassHead at: 0x7f1234567890

=== SCAN #1 ===
[CLASS] C_BaseEntity
[NETVAR] C_BaseEntity.m_iHealth = 0x334
[NETVAR] C_BaseEntity.m_iTeamNum = 0x3cb
[NETVAR] C_BaseEntity.m_vecOrigin = 0x1264
[CLASS] C_CSPlayerPawn
[NETVAR] C_CSPlayerPawn.m_bIsScoped = 0x1450
...
```

## File Structure

- `src/memory.h` - Memory reading/writing and process management
- `src/netvar_manager.h` - Main netvar scanning logic
- `src/pattern_scanner.h` - Byte pattern scanning utilities
- `src/main.cpp` - Main application entry point
- `Makefile` - Build configuration

## License

Educational use only. Do not use for cheating in public games.
