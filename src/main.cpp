#include "memory.h"
#include "netvar_manager.h"
#include <iostream>
#include <thread>
#include <chrono>

int main() {
    std::cout << "=== CS2 Netvar Manager ===" << std::endl;
    std::cout << "Make sure CS2 is running with -insecure and -allow_third_party_software flags!" << std::endl;
    std::cout << "This tool will scan for netvars in real-time." << std::endl << std::endl;
    
    // Create memory instance
    Memory memory;
    
    // Find CS2 process
    std::cout << "[INFO] Looking for CS2 process..." << std::endl;
    if (!memory.FindProcess("cs2")) {
        std::cout << "[ERROR] CS2 process not found! Make sure the game is running." << std::endl;
        std::cout << "[INFO] Process name should be 'cs2' - check with 'ps aux | grep cs2'" << std::endl;
        return 1;
    }
    
    std::cout << "[INFO] Found CS2 process (PID: " << memory.GetProcessId() << ")" << std::endl;
    
    // Attach to process
    std::cout << "[INFO] Attaching to CS2 process..." << std::endl;
    if (!memory.Attach()) {
        std::cout << "[ERROR] Failed to attach to CS2 process!" << std::endl;
        std::cout << "[INFO] Make sure you're running as root or have CAP_SYS_PTRACE capability." << std::endl;
        return 1;
    }
    
    std::cout << "[INFO] Successfully attached to CS2!" << std::endl;
    
    // Create netvar manager
    NetvarManager netvar_manager(&memory);
    
    // Initialize netvar manager
    if (!netvar_manager.Initialize()) {
        std::cout << "[ERROR] Failed to initialize netvar manager!" << std::endl;
        return 1;
    }
    
    // Main loop - scan netvars periodically
    std::cout << "\n[INFO] Starting real-time netvar scanning..." << std::endl;
    std::cout << "[INFO] Press Ctrl+C to exit." << std::endl << std::endl;
    
    int scan_count = 0;
    while (true) {
        scan_count++;
        std::cout << "\n=== SCAN #" << scan_count << " ===" << std::endl;
        
        auto start_time = std::chrono::high_resolution_clock::now();
        
        if (netvar_manager.ScanNetvars()) {
            auto end_time = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
            
            std::cout << "[INFO] Scan completed in " << duration.count() << "ms" << std::endl;
            
            // Print some important netvars if found
            std::cout << "\n=== IMPORTANT NETVARS ===" << std::endl;
            
            // Common CS2 netvars to look for
            std::vector<std::string> important_netvars = {
                "C_BaseEntity.m_iHealth",
                "C_BaseEntity.m_iTeamNum",
                "C_BaseEntity.m_vecOrigin",
                "C_BaseEntity.m_angRotation",
                "C_CSPlayerPawn.m_bIsScoped",
                "C_CSPlayerPawn.m_iShotsFired",
                "C_CSPlayerPawn.m_aimPunchAngle",
                "C_CSPlayerPawn.m_bHasDefuser",
                "C_CSPlayerPawn.m_ArmorValue",
                "C_BasePlayerPawn.m_hController"
            };
            
            for (const auto& netvar : important_netvars) {
                int offset = netvar_manager.GetNetvar(netvar);
                if (offset != -1) {
                    std::cout << "[FOUND] " << netvar << " = 0x" << std::hex << offset << std::dec << std::endl;
                }
            }
            
            std::cout << "=========================" << std::endl;
        } else {
            std::cout << "[ERROR] Netvar scan failed!" << std::endl;
        }
        
        // Wait before next scan
        std::cout << "\n[INFO] Waiting 10 seconds before next scan..." << std::endl;
        std::this_thread::sleep_for(std::chrono::seconds(10));
    }
    
    return 0;
}
