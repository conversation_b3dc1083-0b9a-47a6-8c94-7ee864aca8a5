#pragma once
#include "memory.h"
#include <vector>
#include <string>
#include <sstream>

class PatternScanner {
private:
    Memory* memory;
    
    std::vector<int> ParsePattern(const std::string& pattern) {
        std::vector<int> bytes;
        std::istringstream iss(pattern);
        std::string byte_str;
        
        while (iss >> byte_str) {
            if (byte_str == "??" || byte_str == "?") {
                bytes.push_back(-1); // Wildcard
            } else {
                bytes.push_back(std::stoi(byte_str, nullptr, 16));
            }
        }
        return bytes;
    }
    
    bool CompareBytes(const std::vector<uint8_t>& data, size_t offset, const std::vector<int>& pattern) {
        for (size_t i = 0; i < pattern.size(); ++i) {
            if (offset + i >= data.size()) return false;
            if (pattern[i] != -1 && data[offset + i] != pattern[i]) return false;
        }
        return true;
    }

public:
    PatternScanner(Memory* mem) : memory(mem) {}
    
    uintptr_t FindPattern(uintptr_t start_address, size_t scan_size, const std::string& pattern) {
        if (!memory || !memory->IsAttached()) return 0;
        
        std::vector<int> pattern_bytes = ParsePattern(pattern);
        if (pattern_bytes.empty()) return 0;
        
        // Read memory in chunks to avoid reading too much at once
        const size_t chunk_size = 4096;
        std::vector<uint8_t> buffer(chunk_size);
        
        for (size_t offset = 0; offset < scan_size; offset += chunk_size - pattern_bytes.size()) {
            size_t read_size = std::min(chunk_size, scan_size - offset);
            
            // Read chunk from memory
            std::string mem_path = "/proc/" + std::to_string(memory->GetProcessId()) + "/mem";
            std::ifstream mem_file(mem_path, std::ios::binary);
            if (!mem_file.is_open()) continue;
            
            mem_file.seekg(start_address + offset);
            mem_file.read(reinterpret_cast<char*>(buffer.data()), read_size);
            size_t bytes_read = mem_file.gcount();
            mem_file.close();
            
            if (bytes_read < pattern_bytes.size()) continue;
            
            // Search for pattern in this chunk
            for (size_t i = 0; i <= bytes_read - pattern_bytes.size(); ++i) {
                if (CompareBytes(buffer, i, pattern_bytes)) {
                    return start_address + offset + i;
                }
            }
        }
        
        return 0;
    }
    
    // Common CS2 patterns (these may need updating for your CS2 version)
    uintptr_t FindClientClassHead(uintptr_t client_base, size_t module_size) {
        std::cout << "[INFO] Scanning for ClientClassHead pattern..." << std::endl;
        
        // This pattern may need to be updated for your CS2 version
        // You'll need to reverse engineer or find the correct pattern
        std::string pattern = "48 8B 05 ?? ?? ?? ?? 48 85 C0 74 ?? 8B 50 ??";
        
        uintptr_t result = FindPattern(client_base, module_size, pattern);
        if (result) {
            // Read the relative offset and calculate absolute address
            int32_t relative_offset = memory->Read<int32_t>(result + 3);
            uintptr_t absolute_address = result + 7 + relative_offset;
            std::cout << "[INFO] Found ClientClassHead pattern at: 0x" << std::hex << result << std::dec << std::endl;
            std::cout << "[INFO] ClientClassHead pointer at: 0x" << std::hex << absolute_address << std::dec << std::endl;
            return absolute_address;
        }
        
        std::cout << "[WARNING] ClientClassHead pattern not found!" << std::endl;
        return 0;
    }
    
    // Alternative method - try different known patterns
    uintptr_t FindClientClassHeadAlternative(uintptr_t client_base, size_t module_size) {
        std::cout << "[INFO] Trying alternative ClientClassHead patterns..." << std::endl;
        
        // Alternative patterns that might work
        std::vector<std::string> patterns = {
            "48 8B 0D ?? ?? ?? ?? 48 85 C9 0F 84",
            "48 8B 15 ?? ?? ?? ?? 48 85 D2 74",
            "4C 8B 05 ?? ?? ?? ?? 4D 85 C0 74"
        };
        
        for (const auto& pattern : patterns) {
            uintptr_t result = FindPattern(client_base, module_size, pattern);
            if (result) {
                int32_t relative_offset = memory->Read<int32_t>(result + 3);
                uintptr_t absolute_address = result + 7 + relative_offset;
                std::cout << "[INFO] Found alternative pattern at: 0x" << std::hex << result << std::dec << std::endl;
                return absolute_address;
            }
        }
        
        return 0;
    }
};
