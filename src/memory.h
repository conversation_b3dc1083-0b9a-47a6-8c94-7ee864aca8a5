#pragma once
#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <cstdint>
#include <unistd.h>
#include <sys/ptrace.h>
#include <sys/wait.h>
#include <dirent.h>
#include <cstring>

class Memory {
private:
    pid_t process_id;
    bool attached;

public:
    Memory() : process_id(-1), attached(false) {}
    ~Memory() {
        if (attached) {
            ptrace(PTRACE_DETACH, process_id, nullptr, nullptr);
        }
    }

    bool FindProcess(const std::string& process_name) {
        DIR* proc_dir = opendir("/proc");
        if (!proc_dir) return false;

        struct dirent* entry;
        while ((entry = readdir(proc_dir)) != nullptr) {
            if (!isdigit(entry->d_name[0])) continue;

            std::string comm_path = "/proc/" + std::string(entry->d_name) + "/comm";
            std::ifstream comm_file(comm_path);
            if (!comm_file.is_open()) continue;

            std::string comm;
            std::getline(comm_file, comm);
            comm_file.close();

            if (comm == process_name) {
                process_id = std::stoi(entry->d_name);
                closedir(proc_dir);
                return true;
            }
        }
        closedir(proc_dir);
        return false;
    }

    bool Attach() {
        if (process_id == -1) return false;
        
        if (ptrace(PTRACE_ATTACH, process_id, nullptr, nullptr) == -1) {
            perror("ptrace attach failed");
            return false;
        }
        
        int status;
        waitpid(process_id, &status, 0);
        attached = true;
        return true;
    }

    uintptr_t GetModuleBase(const std::string& module_name) {
        std::string maps_path = "/proc/" + std::to_string(process_id) + "/maps";
        std::ifstream maps_file(maps_path);
        if (!maps_file.is_open()) return 0;

        std::string line;
        while (std::getline(maps_file, line)) {
            if (line.find(module_name) != std::string::npos) {
                size_t dash_pos = line.find('-');
                if (dash_pos != std::string::npos) {
                    std::string addr_str = line.substr(0, dash_pos);
                    return std::stoull(addr_str, nullptr, 16);
                }
            }
        }
        return 0;
    }

    template<typename T>
    T Read(uintptr_t address) {
        T value = {};
        if (!attached) return value;

        std::string mem_path = "/proc/" + std::to_string(process_id) + "/mem";
        std::ifstream mem_file(mem_path, std::ios::binary);
        if (!mem_file.is_open()) return value;

        mem_file.seekg(address);
        mem_file.read(reinterpret_cast<char*>(&value), sizeof(T));
        mem_file.close();
        return value;
    }

    std::string ReadString(uintptr_t address, size_t max_length = 256) {
        if (!attached) return "";

        std::string mem_path = "/proc/" + std::to_string(process_id) + "/mem";
        std::ifstream mem_file(mem_path, std::ios::binary);
        if (!mem_file.is_open()) return "";

        mem_file.seekg(address);
        std::string result;
        result.reserve(max_length);
        
        char c;
        for (size_t i = 0; i < max_length; ++i) {
            mem_file.read(&c, 1);
            if (c == '\0') break;
            result += c;
        }
        
        mem_file.close();
        return result;
    }

    pid_t GetProcessId() const { return process_id; }
    bool IsAttached() const { return attached; }
};
