#pragma once
#include "memory.h"
#include "pattern_scanner.h"
#include <unordered_map>
#include <vector>

// CS2 structures for netvar parsing
struct RecvProp {
    char* prop_name;
    int prop_type;
    int prop_flags;
    int buffer_size;
    int is_inside_array;
    void* extra_data;
    void* array_prop;
    void* array_length_proxy;
    void* proxy_fn;
    void* data_table_proxy_fn;
    void* data_table;
    int offset;
    int element_stride;
    int num_elements;
    char* parent_array_prop_name;
};

struct RecvTable {
    RecvProp* props;
    int num_props;
    void* decoder;
    char* net_table_name;
    bool is_initialized;
    bool in_main_list;
};

struct ClientClass {
    void* create_fn;
    void* create_event_fn;
    char* network_name;
    RecvTable* recv_table;
    ClientClass* next;
    int class_id;
};

class NetvarManager {
private:
    Memory* memory;
    uintptr_t client_base;
    std::unordered_map<std::string, int> netvars;
    
    void ParseRecvTable(RecvTable* table, const std::string& base_name = "") {
        if (!table) return;
        
        // Read the table from memory
        RecvTable recv_table = memory->Read<RecvTable>(reinterpret_cast<uintptr_t>(table));
        
        // Read table name
        std::string table_name = "";
        if (recv_table.net_table_name) {
            table_name = memory->ReadString(reinterpret_cast<uintptr_t>(recv_table.net_table_name));
        }
        
        // Read props array
        if (!recv_table.props || recv_table.num_props <= 0) return;
        
        for (int i = 0; i < recv_table.num_props; ++i) {
            uintptr_t prop_addr = reinterpret_cast<uintptr_t>(recv_table.props) + (i * sizeof(RecvProp));
            RecvProp prop = memory->Read<RecvProp>(prop_addr);
            
            if (!prop.prop_name) continue;
            
            std::string prop_name = memory->ReadString(reinterpret_cast<uintptr_t>(prop.prop_name));
            if (prop_name.empty()) continue;
            
            std::string full_name = base_name.empty() ? prop_name : base_name + "." + prop_name;
            
            // Store the netvar
            if (prop.offset > 0) {
                netvars[full_name] = prop.offset;
                std::cout << "[NETVAR] " << full_name << " = 0x" << std::hex << prop.offset << std::dec << std::endl;
            }
            
            // Recursively parse data tables
            if (prop.data_table) {
                ParseRecvTable(reinterpret_cast<RecvTable*>(prop.data_table), full_name);
            }
        }
    }

public:
    NetvarManager(Memory* mem) : memory(mem), client_base(0) {}
    
    bool Initialize() {
        if (!memory || !memory->IsAttached()) {
            std::cout << "[ERROR] Memory not attached!" << std::endl;
            return false;
        }
        
        // Find client.so base address
        client_base = memory->GetModuleBase("client.so");
        if (!client_base) {
            std::cout << "[ERROR] Could not find client.so module!" << std::endl;
            return false;
        }
        
        std::cout << "[INFO] client.so base: 0x" << std::hex << client_base << std::dec << std::endl;
        return true;
    }
    
    bool ScanNetvars() {
        if (!client_base) {
            std::cout << "[ERROR] Client base not found!" << std::endl;
            return false;
        }
        
        netvars.clear();
        std::cout << "[INFO] Starting netvar scan..." << std::endl;
        
        // We need to find the ClientClassHead pointer
        // This usually requires pattern scanning or known offsets
        // For now, let's try a simple approach - scan for the pattern
        
        // This is a simplified approach - in reality you'd need to pattern scan
        // for the ClientClassHead or use known signatures
        uintptr_t client_class_head = FindClientClassHead();
        
        if (!client_class_head) {
            std::cout << "[ERROR] Could not find ClientClassHead!" << std::endl;
            return false;
        }
        
        std::cout << "[INFO] Found ClientClassHead at: 0x" << std::hex << client_class_head << std::dec << std::endl;
        
        // Parse client classes
        ParseClientClasses(client_class_head);
        
        std::cout << "[INFO] Netvar scan complete! Found " << netvars.size() << " netvars." << std::endl;
        return true;
    }
    
    int GetNetvar(const std::string& name) {
        auto it = netvars.find(name);
        return (it != netvars.end()) ? it->second : -1;
    }
    
    void PrintAllNetvars() {
        std::cout << "\n=== ALL NETVARS ===" << std::endl;
        for (const auto& pair : netvars) {
            std::cout << pair.first << " = 0x" << std::hex << pair.second << std::dec << std::endl;
        }
        std::cout << "===================" << std::endl;
    }

private:
    uintptr_t FindClientClassHead() {
        PatternScanner scanner(memory);

        // Get module size for scanning
        std::string maps_path = "/proc/" + std::to_string(memory->GetProcessId()) + "/maps";
        std::ifstream maps_file(maps_path);
        size_t module_size = 0x1000000; // Default 16MB scan size

        if (maps_file.is_open()) {
            std::string line;
            while (std::getline(maps_file, line)) {
                if (line.find("client.so") != std::string::npos) {
                    size_t dash_pos = line.find('-');
                    size_t space_pos = line.find(' ');
                    if (dash_pos != std::string::npos && space_pos != std::string::npos) {
                        std::string start_addr = line.substr(0, dash_pos);
                        std::string end_addr = line.substr(dash_pos + 1, space_pos - dash_pos - 1);
                        uintptr_t start = std::stoull(start_addr, nullptr, 16);
                        uintptr_t end = std::stoull(end_addr, nullptr, 16);
                        module_size = end - start;
                        break;
                    }
                }
            }
            maps_file.close();
        }

        std::cout << "[INFO] Scanning client.so (size: 0x" << std::hex << module_size << std::dec << ")" << std::endl;

        // Try primary pattern first
        uintptr_t result = scanner.FindClientClassHead(client_base, module_size);
        if (result) return result;

        // Try alternative patterns
        result = scanner.FindClientClassHeadAlternative(client_base, module_size);
        if (result) return result;

        std::cout << "[ERROR] Could not find ClientClassHead with any known patterns!" << std::endl;
        return 0;
    }
    
    void ParseClientClasses(uintptr_t class_head_ptr) {
        uintptr_t current_class = memory->Read<uintptr_t>(class_head_ptr);
        
        while (current_class) {
            ClientClass client_class = memory->Read<ClientClass>(current_class);
            
            if (client_class.network_name) {
                std::string class_name = memory->ReadString(reinterpret_cast<uintptr_t>(client_class.network_name));
                std::cout << "[CLASS] " << class_name << std::endl;
                
                if (client_class.recv_table) {
                    ParseRecvTable(client_class.recv_table, class_name);
                }
            }
            
            current_class = reinterpret_cast<uintptr_t>(client_class.next);
        }
    }
};
